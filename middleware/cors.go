package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")

		// 设置CORS头部
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.Header("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		// 记录跨域请求
		if origin != "" {
			c.Set("cors_origin", origin)
		}

		c.Next()
	}
}

// CustomCORS 自定义CORS中间件
func CustomCORS(allowOrigins []string, allowMethods []string, allowHeaders []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")

		// 检查是否允许该来源
		allowedOrigin := "*"
		if len(allowOrigins) > 0 {
			allowedOrigin = ""
			for _, allowedOrig := range allowOrigins {
				if allowedOrig == origin || allowedOrig == "*" {
					allowedOrigin = origin
					break
				}
			}
			if allowedOrigin == "" {
				c.AbortWithStatus(http.StatusForbidden)
				return
			}
		}

		// 设置允许的方法
		allowedMethods := "GET, POST, PUT, DELETE, PATCH, OPTIONS"
		if len(allowMethods) > 0 {
			allowedMethods = ""
			for i, method := range allowMethods {
				if i > 0 {
					allowedMethods += ", "
				}
				allowedMethods += method
			}
		}

		// 设置允许的头部
		allowedHeaders := "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With"
		if len(allowHeaders) > 0 {
			allowedHeaders = ""
			for i, header := range allowHeaders {
				if i > 0 {
					allowedHeaders += ", "
				}
				allowedHeaders += header
			}
		}

		// 设置CORS头部
		c.Header("Access-Control-Allow-Origin", allowedOrigin)
		c.Header("Access-Control-Allow-Methods", allowedMethods)
		c.Header("Access-Control-Allow-Headers", allowedHeaders)
		c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.Header("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
