package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// Logger 自定义日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %s %d %s %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.ClientIP,
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// RequestLogger 详细请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)

		// 获取状态码
		statusCode := c.Writer.Status()

		// 构建日志信息
		logData := map[string]interface{}{
			"timestamp":   start.Format("2006-01-02 15:04:05"),
			"method":      c.Request.Method,
			"path":        path,
			"query":       raw,
			"status_code": statusCode,
			"latency":     latency.String(),
			"client_ip":   c.ClientIP(),
			"user_agent":  c.Request.UserAgent(),
			"referer":     c.Request.Referer(),
		}

		// 添加请求头信息（可选）
		if gin.Mode() == gin.DebugMode {
			headers := make(map[string]string)
			for key, values := range c.Request.Header {
				if len(values) > 0 {
					headers[key] = values[0]
				}
			}
			logData["headers"] = headers
		}

		// 根据状态码选择日志级别
		if statusCode >= 500 {
			fmt.Printf("[ERROR] %+v\n", logData)
		} else if statusCode >= 400 {
			fmt.Printf("[WARN] %+v\n", logData)
		} else {
			fmt.Printf("[INFO] %+v\n", logData)
		}
	}
}

// Recovery 自定义恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			fmt.Printf("[PANIC] %s: %s\n", time.Now().Format("2006-01-02 15:04:05"), err)
			c.JSON(500, gin.H{
				"error": "Internal server error",
				"code":  "PANIC_RECOVERED",
			})
		}
		c.Abort()
	})
}
