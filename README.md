# Gin Reverse Proxy

基于 Gin 框架的高性能反向代理服务器，支持负载均衡、健康检查、CORS 等功能。

## 功能特性

- 🚀 基于 Gin 框架，高性能
- 🔄 支持多种负载均衡策略（权重轮询）
- 🏥 内置健康检查
- 🌐 CORS 跨域支持
- 📝 详细的请求日志记录
- ⚙️ 灵活的配置管理
- 🛡️ 优雅的错误处理和恢复
- 🔧 支持路径重写和请求转发

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 运行服务

```bash
# 使用默认配置
go run main.go

# 指定配置文件
go run main.go -config custom-config.json

# 查看版本信息
go run main.go -version
```

### 构建可执行文件

```bash
go build -o reverse-proxy main.go
./reverse-proxy -config config.json
```

## 配置说明

配置文件采用 JSON 格式，首次运行时会自动生成默认配置文件 `config.json`。

### 配置示例

```json
{
  "port": 8080,
  "host": "0.0.0.0",
  "enable_cors": true,
  "enable_log": true,
  "health_check": true,
  "read_timeout": 30,
  "write_timeout": 30,
  "rules": [
    {
      "path": "/api/v1/*",
      "method": "",
      "targets": [
        {
          "url": "http://localhost:3001",
          "weight": 1
        }
      ]
    },
    {
      "path": "/api/v2/*",
      "method": "",
      "targets": [
        {
          "url": "http://localhost:3002",
          "weight": 2
        },
        {
          "url": "http://localhost:3003",
          "weight": 1
        }
      ]
    }
  ]
}
```

### 配置参数说明

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `port` | int | 监听端口 | 8080 |
| `host` | string | 监听地址 | "0.0.0.0" |
| `enable_cors` | bool | 是否启用CORS | true |
| `enable_log` | bool | 是否启用日志 | true |
| `health_check` | bool | 是否启用健康检查 | true |
| `read_timeout` | int | 读取超时（秒） | 30 |
| `write_timeout` | int | 写入超时（秒） | 30 |
| `rules` | array | 代理规则列表 | - |

### 代理规则配置

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `path` | string | 匹配路径，支持通配符 | "/api/v1/*" |
| `method` | string | HTTP方法，空表示所有方法 | "GET", "POST", "" |
| `targets` | array | 目标服务器列表 | - |

### 目标服务器配置

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `url` | string | 目标服务器URL | "http://localhost:3001" |
| `weight` | int | 权重，用于负载均衡 | 1 |

## API 端点

### 健康检查

```bash
GET /health
GET /_health
```

返回代理服务器和目标服务器的健康状态。

### 状态信息

```bash
GET /status
```

返回服务器运行状态和配置信息。

### 调试信息（仅调试模式）

```bash
GET /debug/config
```

返回完整的配置信息。

## 使用示例

### 1. 基本代理

配置将 `/api/*` 的请求代理到 `http://localhost:3000`：

```json
{
  "rules": [
    {
      "path": "/api/*",
      "targets": [
        {"url": "http://localhost:3000", "weight": 1}
      ]
    }
  ]
}
```

### 2. 负载均衡

配置多个目标服务器进行负载均衡：

```json
{
  "rules": [
    {
      "path": "/api/*",
      "targets": [
        {"url": "http://localhost:3001", "weight": 2},
        {"url": "http://localhost:3002", "weight": 1}
      ]
    }
  ]
}
```

### 3. 方法限制

只代理特定HTTP方法的请求：

```json
{
  "rules": [
    {
      "path": "/api/upload",
      "method": "POST",
      "targets": [
        {"url": "http://localhost:3001", "weight": 1}
      ]
    }
  ]
}
```

## 开发

### 项目结构

```
.
├── main.go              # 主程序入口
├── config/
│   └── config.go        # 配置管理
├── proxy/
│   └── handler.go       # 代理处理器
├── middleware/
│   ├── logging.go       # 日志中间件
│   └── cors.go          # CORS中间件
├── go.mod
├── go.sum
└── README.md
```

### 运行测试

```bash
go test ./...
```

## 许可证

MIT License
