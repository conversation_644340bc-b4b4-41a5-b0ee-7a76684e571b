package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"xkgo/reverse-proxy/config"
	"xkgo/reverse-proxy/middleware"
	"xkgo/reverse-proxy/proxy"
)

var (
	configFile = flag.String("config", "config.json", "配置文件路径")
	version    = flag.Bool("version", false, "显示版本信息")
)

const (
	AppName    = "Gin Reverse Proxy"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s v%s\n", AppName, AppVersion)
		os.Exit(0)
	}

	// 加载配置
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置Gin模式
	if cfg.EnableLog {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	if cfg.EnableLog {
		r.Use(middleware.Logger())
		r.Use(middleware.RequestLogger())
	}

	r.Use(middleware.Recovery())

	if cfg.EnableCORS {
		r.Use(middleware.CORS())
	}

	// 创建代理处理器
	proxyHandler := proxy.NewProxyHandler(cfg)

	// 设置路由
	setupRoutes(r, proxyHandler, cfg)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:         cfg.GetAddress(),
		Handler:      r,
		ReadTimeout:  time.Duration(cfg.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.WriteTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		log.Printf("Starting %s v%s on %s", AppName, AppVersion, cfg.GetAddress())
		log.Printf("Config file: %s", *configFile)
		log.Printf("Proxy rules: %d", len(cfg.Rules))
		
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	} else {
		log.Println("Server exited")
	}
}

// setupRoutes 设置路由
func setupRoutes(r *gin.Engine, proxyHandler *proxy.ProxyHandler, cfg *config.Config) {
	// 健康检查路由
	if cfg.HealthCheck {
		r.GET("/health", proxyHandler.HealthCheck)
		r.GET("/_health", proxyHandler.HealthCheck)
	}

	// 状态信息路由
	r.GET("/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"app":     AppName,
			"version": AppVersion,
			"status":  "running",
			"config": gin.H{
				"port":         cfg.Port,
				"rules":        len(cfg.Rules),
				"enable_cors":  cfg.EnableCORS,
				"enable_log":   cfg.EnableLog,
				"health_check": cfg.HealthCheck,
			},
		})
	})

	// 配置信息路由（仅在调试模式下）
	if gin.Mode() == gin.DebugMode {
		r.GET("/debug/config", func(c *gin.Context) {
			c.JSON(http.StatusOK, cfg)
		})
	}

	// 代理路由 - 使用通配符捕获所有请求
	r.Any("/*path", proxyHandler.HandleProxy)
}
