package main

import (
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"

	"github.com/gin-gonic/gin"
)

func main() {
	// 目标服务器URL
	targetURL, err := url.Parse("https://hd-test.yy.com")
	if err != nil {
		log.Fatal("Invalid target URL:", err)
	}

	// 创建反向代理
	proxy := httputil.NewSingleHostReverseProxy(targetURL)

	// 创建Gin路由器
	r := gin.Default()

	// 代理所有请求到目标服务器
	r.Any("/zhuiya_recommend/v2/get_top_info", func(c *gin.Context) {
		proxy.ServeHTTP(c.Writer, c.Request)
	})
	r.Any("/zhuiya_recommend/v2/get_banner_info", func(c *gin.Context) {
		proxy.ServeHTTP(c.Writer, c.Request)
	})

	// 启动服务器
	log.Println("Starting proxy server on :8081")
	log.Println("Proxying all requests to https://hd-test.yy.com")
	r.Run(":8081")
}
