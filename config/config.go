package config

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
)

// ProxyTarget 代理目标配置
type ProxyTarget struct {
	URL    string `json:"url"`    // 目标服务器URL
	Weight int    `json:"weight"` // 权重，用于负载均衡
}

// ProxyRule 代理规则配置
type ProxyRule struct {
	Path    string         `json:"path"`    // 匹配路径
	Targets []*ProxyTarget `json:"targets"` // 目标服务器列表
	Method  string         `json:"method"`  // HTTP方法，空表示所有方法
}

// Config 应用配置
type Config struct {
	Port         int          `json:"port"`         // 监听端口
	Host         string       `json:"host"`         // 监听地址
	Rules        []*ProxyRule `json:"rules"`        // 代理规则
	EnableCORS   bool         `json:"enable_cors"`  // 是否启用CORS
	EnableLog    bool         `json:"enable_log"`   // 是否启用日志
	HealthCheck  bool         `json:"health_check"` // 是否启用健康检查
	ReadTimeout  int          `json:"read_timeout"` // 读取超时（秒）
	WriteTimeout int          `json:"write_timeout"`// 写入超时（秒）
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Port:         8080,
		Host:         "0.0.0.0",
		EnableCORS:   true,
		EnableLog:    true,
		HealthCheck:  true,
		ReadTimeout:  30,
		WriteTimeout: 30,
		Rules: []*ProxyRule{
			{
				Path:   "/api/v1/*",
				Method: "",
				Targets: []*ProxyTarget{
					{URL: "http://localhost:3001", Weight: 1},
				},
			},
			{
				Path:   "/api/v2/*",
				Method: "",
				Targets: []*ProxyTarget{
					{URL: "http://localhost:3002", Weight: 1},
				},
			},
		},
	}
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	// 如果文件不存在，创建默认配置文件
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		config := DefaultConfig()
		if err := SaveConfig(config, filename); err != nil {
			return nil, fmt.Errorf("failed to create default config: %v", err)
		}
		return config, nil
	}

	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %v", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %v", err)
	}

	return &config, nil
}

// SaveConfig 保存配置到文件
func SaveConfig(config *Config, filename string) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}

	return ioutil.WriteFile(filename, data, 0644)
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("invalid port: %d", c.Port)
	}

	if len(c.Rules) == 0 {
		return fmt.Errorf("no proxy rules defined")
	}

	for i, rule := range c.Rules {
		if rule.Path == "" {
			return fmt.Errorf("rule %d: path cannot be empty", i)
		}

		if len(rule.Targets) == 0 {
			return fmt.Errorf("rule %d: no targets defined", i)
		}

		for j, target := range rule.Targets {
			if _, err := url.Parse(target.URL); err != nil {
				return fmt.Errorf("rule %d, target %d: invalid URL %s: %v", i, j, target.URL, err)
			}

			if target.Weight <= 0 {
				return fmt.Errorf("rule %d, target %d: weight must be positive", i, j)
			}
		}
	}

	return nil
}

// GetAddress 获取监听地址
func (c *Config) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}
