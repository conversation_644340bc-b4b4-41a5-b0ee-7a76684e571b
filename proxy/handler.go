package proxy

import (
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"xkgo/reverse-proxy/config"
)

// ProxyHandler 反向代理处理器
type ProxyHandler struct {
	config *config.Config
	rand   *rand.Rand
}

// NewProxyHandler 创建新的代理处理器
func NewProxyHandler(cfg *config.Config) *ProxyHandler {
	return &ProxyHandler{
		config: cfg,
		rand:   rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// HandleProxy 处理代理请求
func (p *ProxyHandler) HandleProxy(c *gin.Context) {
	path := c.Request.URL.Path
	method := c.Request.Method

	// 查找匹配的代理规则
	rule := p.findMatchingRule(path, method)
	if rule == nil {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"error": "No matching proxy rule found",
			"path":  path,
		})
		return
	}

	// 选择目标服务器
	target := p.selectTarget(rule.Targets)
	if target == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "No available target servers",
		})
		return
	}

	// 解析目标URL
	targetURL, err := url.Parse(target.URL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Invalid target URL: %v", err),
		})
		return
	}

	// 创建反向代理
	proxy := httputil.NewSingleHostReverseProxy(targetURL)
	
	// 自定义Director函数来修改请求
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		
		// 移除路径前缀（如果需要）
		if strings.HasSuffix(rule.Path, "/*") {
			prefix := strings.TrimSuffix(rule.Path, "/*")
			if strings.HasPrefix(req.URL.Path, prefix) {
				req.URL.Path = strings.TrimPrefix(req.URL.Path, prefix)
				if req.URL.Path == "" {
					req.URL.Path = "/"
				}
			}
		}
		
		// 设置必要的头部
		req.Header.Set("X-Forwarded-Host", req.Host)
		req.Header.Set("X-Forwarded-Proto", "http")
		if c.ClientIP() != "" {
			req.Header.Set("X-Forwarded-For", c.ClientIP())
		}
	}

	// 自定义错误处理
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		c.JSON(http.StatusBadGateway, gin.H{
			"error":  "Proxy error",
			"detail": err.Error(),
			"target": target.URL,
		})
	}

	// 执行代理
	proxy.ServeHTTP(c.Writer, c.Request)
}

// findMatchingRule 查找匹配的代理规则
func (p *ProxyHandler) findMatchingRule(path, method string) *config.ProxyRule {
	for _, rule := range p.config.Rules {
		// 检查方法匹配
		if rule.Method != "" && rule.Method != method {
			continue
		}

		// 检查路径匹配
		if p.pathMatches(path, rule.Path) {
			return rule
		}
	}
	return nil
}

// pathMatches 检查路径是否匹配规则
func (p *ProxyHandler) pathMatches(path, pattern string) bool {
	// 精确匹配
	if path == pattern {
		return true
	}

	// 通配符匹配
	if strings.HasSuffix(pattern, "/*") {
		prefix := strings.TrimSuffix(pattern, "/*")
		return strings.HasPrefix(path, prefix)
	}

	// 前缀匹配
	if strings.HasSuffix(pattern, "/") {
		return strings.HasPrefix(path, pattern)
	}

	return false
}

// selectTarget 根据权重选择目标服务器
func (p *ProxyHandler) selectTarget(targets []*config.ProxyTarget) *config.ProxyTarget {
	if len(targets) == 0 {
		return nil
	}

	if len(targets) == 1 {
		return targets[0]
	}

	// 计算总权重
	totalWeight := 0
	for _, target := range targets {
		totalWeight += target.Weight
	}

	// 随机选择
	randomWeight := p.rand.Intn(totalWeight)
	currentWeight := 0

	for _, target := range targets {
		currentWeight += target.Weight
		if randomWeight < currentWeight {
			return target
		}
	}

	// 默认返回第一个
	return targets[0]
}

// HealthCheck 健康检查处理器
func (p *ProxyHandler) HealthCheck(c *gin.Context) {
	status := gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
		"rules":     len(p.config.Rules),
	}

	// 检查目标服务器状态
	targets := make(map[string]string)
	for _, rule := range p.config.Rules {
		for _, target := range rule.Targets {
			if _, exists := targets[target.URL]; !exists {
				targets[target.URL] = p.checkTargetHealth(target.URL)
			}
		}
	}

	status["targets"] = targets
	c.JSON(http.StatusOK, status)
}

// checkTargetHealth 检查目标服务器健康状态
func (p *ProxyHandler) checkTargetHealth(targetURL string) string {
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(targetURL + "/health")
	if err != nil {
		return "unhealthy: " + err.Error()
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		return "healthy"
	}

	body, _ := io.ReadAll(resp.Body)
	return fmt.Sprintf("unhealthy: status %d, body: %s", resp.StatusCode, string(body))
}
